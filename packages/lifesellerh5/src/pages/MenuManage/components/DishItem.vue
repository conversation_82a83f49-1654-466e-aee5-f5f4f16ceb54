<template>
  <div class="dish-item-edit">
    <!-- 拖拽按钮 - 与菜品卡片居中对齐 -->
    <div class="dish-drag-handle">
      <OnixIcon icon="menu_m" size="24" />
    </div>

    <div class="dish-content" @click="onEdit">
      <img
        :src="dish.dishResourceList?.[0]?.resourceUrl || ''"
        :alt="dish.dishName"
        :width="60"
        :height="60"
        :style="{
          borderRadius: '6px',
          objectFit: 'cover',
          width: '60px',
          height: '60px'
        }"
      />
      <div class="dish-info">
        <div class="dish-name">{{ dish.dishName }}</div>
        <div class="dish-price">
          <span class="dish-price-unit" style="font-size: 12px; line-height: 16px;">¥</span>
          {{ dish.priceItem.dishPrice }}
          <span class="dish-price-unit">{{ dish.priceItem.priceType === PriceType.STARTING ? '起' : '' }}</span>
        </div>
      </div>
    </div>

    <div class="dish-actions">
      <span class="action-btn" @click.stop="onEdit">编辑</span>
      <Divider v-if="!inSpecialtyGroup" direction="vertical" :size="14"></Divider>
      <span v-if="!inSpecialtyGroup" class="action-btn" @click.stop="onDelete">删除</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { Image, Divider } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import { IDishList, PriceType } from '~/types/menu'
  import '~/assets/svg/menu_m.svg'

  interface Props {
    dish: IDishList
    inSpecialtyGroup?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    inSpecialtyGroup: false
  })

  const emit = defineEmits(['edit', 'delete'])

  const onEdit = () => {
    emit('edit', props.dish)
  }

  const onDelete = () => {
    emit('delete', props.dish)
  }
</script>

<style lang="stylus" scoped>
.dish-item-edit
  display flex
  align-items center
  height 60px
  padding 6px 0
  gap 12px
  box-sizing content-box

.dish-drag-handle
  display flex
  align-items center
  color rgba(0, 0, 0, 0.45)

.dish-content
  display flex
  align-items center
  flex 1
  cursor pointer
  height 100%
  gap 12px

  .dish-image
    width 60px
    border-radius 6px
    object-fit cover

  .dish-placeholder
    width 60px
    height 60px
    border-radius 6px
    background-color #f5f5f5
    border 1px dashed #ddd
    display flex
    align-items center
    justify-content center

    span
      font-size 12px
      color #999

  .dish-info
    flex 1
    display flex
    height 100%
    flex-direction column
    justify-content space-between

    .dish-name
      overflow: hidden;
      color: rgba(0, 0, 0, 0.8);
      text-overflow: ellipsis;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;

    .dish-price
      color: rgba(0, 0, 0, 0.8);
      font-family: "RED Number";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;

    .dish-price-unit
      font-size 10px
      color rgba(0, 0, 0, 0.8)
      font-weight 500
      line-height 14px

.dish-actions
  display flex
  height 100%
  align-items center

  .action-btn
    font-size 14px
    color rgba(0, 0, 0, 0.62)
</style>
