# MenuEditor 返回校验功能实现

## 功能描述

在菜单编辑页面，当用户点击返回按钮时，系统会校验商家是否变更了菜单内容。如果内容有变更且未保存，会弹窗提示："当前菜单尚未保存，确认退出吗？"，用户需要二次确认后才能退出。

## 实现方案

### 1. 核心函数

#### `hasMenuChanged()` - 检查菜单是否有变更
检查以下几个方面的变更：
- 删除记录（分组或菜品被删除）
- 新增分组（临时分组）
- 分组名称变更
- 分组数量变化
- 菜品数量变化
- 菜品顺序变化

#### `handleBackClick()` - 处理返回点击
- 调用`hasMenuChanged()`检查是否有变更
- 如果有变更，弹窗确认
- 如果没有变更，直接退出

### 2. 变更检查逻辑

#### 2.1 删除记录检查
```javascript
// 检查是否有删除记录
if (deletedGroupIds.value.size > 0 || deletedDishIds.value.size > 0) {
  return true
}
```

#### 2.2 新增分组检查
```javascript
// 检查是否有临时分组（新增分组）
const hasTemporaryGroups = menuGroups.value.some(group => 
  group && group.isTemporary && group.groupId && group.groupId.startsWith('temp_')
)
```

#### 2.3 分组内容变更检查
- 分组数量变化
- 分组名称变化
- 菜品数量变化
- 菜品顺序变化

### 3. 弹窗确认逻辑

```javascript
alertMethodRef.value?.showAlert({
  title: '提示',
  message: '当前菜单尚未保存，确认退出吗？',
  confirmText: '确认退出',
  cancelText: '取消',
  footerLayout: 'horizontal',
  onConfirm: () => {
    router.back() // 确认退出
  },
  onCancel: () => {
    // 取消，不做任何操作
  }
})
```

## 检查场景

### ✅ 会触发确认弹窗的场景

1. **删除操作**
   - 删除了任何分组
   - 删除了任何菜品

2. **新增操作**
   - 添加了新的分组（临时分组）

3. **修改操作**
   - 修改了分组名称
   - 改变了分组数量
   - 改变了菜品数量
   - 改变了菜品排序

### ✅ 不会触发确认弹窗的场景

1. **无任何操作**
   - 进入页面后直接返回

2. **已保存状态**
   - 进行操作后点击了保存按钮

3. **仅浏览操作**
   - 只是查看菜单内容，没有进行任何修改

## 技术细节

### 1. 数据对比基准
使用`previousMenuGroups.value`作为对比基准，这个值在数据加载和保存后会更新，代表最后一次保存的状态。

### 2. 检查粒度
- **分组级别**: 检查分组的增删改
- **菜品级别**: 检查菜品的增删改和排序
- **删除记录**: 检查store中的删除记录

### 3. 性能考虑
- 检查逻辑在用户点击返回时才执行，不影响正常编辑性能
- 使用浅层对比，避免深度递归比较
- 优先检查最容易变化的项目（删除记录、临时分组）

## 用户体验

### 1. 提示信息
- **标题**: "提示"
- **内容**: "当前菜单尚未保存，确认退出吗？"
- **按钮**: "确认退出" / "取消"

### 2. 交互流程
```
用户点击返回 → 检查是否有变更 → 有变更 → 弹窗确认 → 用户选择
                                → 无变更 → 直接退出
```

### 3. 确认后行为
- **确认退出**: 直接调用`router.back()`返回上一页
- **取消**: 关闭弹窗，继续编辑

## 扩展性

### 1. 可扩展的检查项
- 招牌菜变更检查
- 菜品详细信息变更检查
- 分组属性变更检查

### 2. 可配置的行为
- 是否在确认退出时清理删除记录
- 自定义弹窗文案
- 不同类型变更的不同提示

## 注意事项

### 1. 数据一致性
确保`previousMenuGroups.value`在适当的时机更新，以保证对比基准的准确性。

### 2. 边界情况
- 页面首次加载时没有`previousMenuGroups`数据
- 网络异常导致数据不完整
- 用户快速连续操作

### 3. 内存管理
删除记录会持续存在于store中，需要在适当时机清理以避免内存泄漏。

## 总结

这个实现提供了完整的菜单编辑返回校验功能，能够准确检测各种类型的菜单变更，并通过友好的弹窗提示保护用户的编辑成果，避免意外丢失未保存的修改。
